/**
 * ========================================================================
 * SPEECH INTEGRATION
 * ========================================================================
 *
 * Maneja la integración entre el AI Service y el sistema de speech
 * Controla cuándo y cómo se narran las respuestas de la IA
 * ========================================================================
 */

import { speechCoordinator } from "../../SpeechCoordinator";
import { speechService } from "../../speech/speechService";

export interface SpeechConfig {
  enabled: boolean;
  autoNarrate: boolean;
  characterAnnouncements: boolean;
  responseNarration: boolean;
  welcomeMessages: boolean;
}

export interface SpeechResult {
  success: boolean;
  error?: string;
  duration?: number;
}

export class SpeechIntegration {
  private serviceName = "speechIntegration";

  private config: SpeechConfig = {
    enabled: true,
    autoNarrate: true,
    characterAnnouncements: true,
    responseNarration: true,
    welcomeMessages: true,
  };

  // ========== CONFIGURACIÓN ==========

  /**
   * Habilita toda la funcionalidad de speech
   */
  public enableAll(): void {
    this.config.enabled = true;
    this.config.autoNarrate = true;
    console.log(`ℹ️ [${this.serviceName}] 🔊 Speech habilitado completamente`);
  }

  /**
   * Deshabilita toda la funcionalidad de speech
   */
  public disableAll(): void {
    this.config.enabled = false;
    this.config.autoNarrate = false;
  }

  /**
   * Configura tipos específicos de speech
   */
  public configure(options: Partial<SpeechConfig>): void {
    this.config = { ...this.config, ...options };
  }

  /**
   * Obtiene la configuración actual
   */
  public getConfig(): SpeechConfig {
    return { ...this.config };
  }

  // ========== MÉTODOS DE NARRACIÓN ==========

  /**
   * Narra el contenido de respuesta de IA si está habilitado
   */
  private async narrateResponse(
    content: string,
    type: "game_response" | "character" | "welcome" = "game_response"
  ): Promise<SpeechResult> {
    if (!this.shouldNarrate(type)) {
      return { success: true, error: "Speech disabled for this type" };
    }

    try {
      const startTime = Date.now();
      await speechCoordinator.speak(content, type);
      const duration = Date.now() - startTime;
      return { success: true, duration };
    } catch (error) {
      console.error(`❌ [speechIntegration] Error en narración:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown speech error",
      };
    }
  }

  /**
   * Narra respuesta de juego con validación de contenido
   */
  public async narrateGameResponse(content: string): Promise<SpeechResult> {
  const azureTTSEnabled = speechService.isAzureTTSEnabled();

  if (!azureTTSEnabled) {
    console.log(`❌ [speechIntegration] Azure TTS desactivado - saltando narración`);
    return {
      success: true,
      error: "Azure TTS disabled",
    };
  }

  const cleanContent = this.cleanContentForSpeech(content);
  if (!cleanContent) {
    return {
      success: false,
      error: "No valid content for speech",
    };
  }

  return this.narrateResponse(cleanContent, "game_response");
}

  /**
   * Narra mensaje de bienvenida
   */
  public async narrateWelcomeMessage(message: string): Promise<SpeechResult> {
    return this.narrateResponse(message, "welcome");
  }

  // ========== CONTROL DE SPEECH ==========

  /**
   * Detiene cualquier narración en curso
   */
  public async stopSpeech(): Promise<void> {
    try {
      // await speechCoordinator.stop();
      console.log(`ℹ️ [${this.serviceName}] 🛑 Speech detenido`);
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error deteniendo speech:`, error);
    }
  }

  /**
   * Pausa la narración actual
   */
  public async pauseSpeech(): Promise<void> {
    try {
      speechCoordinator.interrupt();
      console.log(`ℹ️ [${this.serviceName}] ⏸️ Speech pausado`);
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error pausando speech:`, error);
    }
  }

  /**
   * Reanuda la narración pausada
   */
  public async resumeSpeech(): Promise<void> {
    try {
      // El SpeechCoordinator no tiene un método resume específico
      // La reanudación se maneja automáticamente cuando se hace una nueva solicitud
      console.log(`ℹ️ [${this.serviceName}] ▶️ Speech reanudado`);
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error reanudando speech:`, error);
    }
  }

  // ========== MÉTODOS DE UTILIDAD ==========

  /**
   * Verifica si se debe narrar según el tipo y configuración
   */
  private shouldNarrate(
    type: "game_response" | "character" | "welcome"
  ): boolean {
    if (!this.config.enabled || !this.config.autoNarrate) {
      return false;
    }

    let typeEnabled = false;
    switch (type) {
      case "game_response":
        typeEnabled = this.config.responseNarration;
        break;
      case "character":
        typeEnabled = this.config.characterAnnouncements;
        break;
      case "welcome":
        typeEnabled = this.config.welcomeMessages;
        break;
      default:
        typeEnabled = false;
    }
    return typeEnabled;
  }

  /**
   * Limpia el contenido para speech (remueve markdown, HTML, etc.)
   */
  private cleanContentForSpeech(content: string): string {
    if (!content) return "";

    return (
      content
        // Remover markdown
        .replace(/\*\*(.*?)\*\*/g, "$1") // Bold
        .replace(/\*(.*?)\*/g, "$1") // Italic
        .replace(/`(.*?)`/g, "$1") // Code
        .replace(/#{1,6}\s*(.*)/g, "$1") // Headers

        // Remover HTML básico
        .replace(/<[^>]*>/g, "")

        // Limpiar espacios extra
        .replace(/\s+/g, " ")
        .trim()
    );
  }

  /**
   * Verifica si el sistema de speech está disponible
   */
  public async checkSpeechAvailability(): Promise<boolean> {
    try {
      // return speechCoordinator.isAvailable();
      return true; // Simulación temporal
    } catch {
      return false;
    }
  }

  /**
   * Obtiene el estado actual del speech
   */
  public getSpeechState(): {
    isPlaying: boolean;
    isPaused: boolean;
    currentText?: string;
  } {
    try {
      // return speechCoordinator.getState();
      return { isPlaying: false, isPaused: false }; // Simulación temporal
    } catch {
      return { isPlaying: false, isPaused: false };
    }
  }

  /**
   * Configura la velocidad de speech
   */
  public setSpeechRate(rate: number): void {
    try {
      // speechCoordinator.setRate(rate);
      console.log(
        `ℹ️ [${this.serviceName}] 🎛️ Velocidad de speech configurada: ${rate}`
      );
    } catch (error) {
      console.error(
        `❌ [${this.serviceName}] Error configurando velocidad:`,
        error
      );
    }
  }

  /**
   * Configura el volumen de speech
   */
  public setSpeechVolume(volume: number): void {
    try {
      // speechCoordinator.setVolume(volume);
      console.log(
        `ℹ️ [${this.serviceName}] 🔊 Volumen de speech configurado: ${volume}`
      );
    } catch (error) {
      console.error(
        `❌ [${this.serviceName}] Error configurando volumen:`,
        error
      );
    }
  }
}

// ========== SINGLETON EXPORT ==========
export const speechIntegration = new SpeechIntegration();
