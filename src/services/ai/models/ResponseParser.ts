/**
 * ========================================================================
 * RESPONSE PARSER
 * ========================================================================
 *
 * Maneja el parseo de respuestas de la IA, incluyendo detección de JSON
 * estructurado y extracción de datos de juego
 * ========================================================================
 */

import type {
  AIGameResponse,
  ParseResult,
  ProcessedAIResponse
} from './AIModels';

export class ResponseParser {
  private serviceName = 'responseParser';

  // ========== DETECCIÓN DE RESPUESTAS ESTRUCTURADAS ==========

  /**
   * Detecta si el output contiene JSON estructurado válido
   */
  public isStructuredResponse(output: string): boolean {
    if (!output || typeof output !== 'string') return false;

    const cleanOutput = output.trim();

    // Buscar bloques de código JSON
    if (cleanOutput.includes('```json') || cleanOutput.includes('```\n{')) {
      return true;
    }

    // Buscar JSON directo (que empiece y termine con llaves)
    if (cleanOutput.startsWith('{') && cleanOutput.endsWith('}')) {
      return true;
    }

    // Buscar patrones JSON dentro del texto
    const jsonPattern = /\{[\s\S]*"respuesta"[\s\S]*"pista"[\s\S]*"acertado"[\s\S]*\}/;
    return jsonPattern.test(cleanOutput);
  }

  // ========== EXTRACCIÓN Y PARSEO ==========

  /**
   * Extrae y parsea la respuesta JSON de la IA desde el output
   */
  public parseAIGameResponse(output: string): ParseResult {
    try {
      if (!output || typeof output !== 'string') {
        return {
          success: false,
          error: 'Output vacío o inválido',
          rawOutput: output
        };
      }

      // Verificar si es una respuesta estructurada
      if (!this.isStructuredResponse(output)) {
        return {
          success: false,
          error: 'Respuesta no estructurada - es texto normal',
          rawOutput: output
        };
      }

      // Limpiar el output
      let cleanOutput = output.trim();

      // Extraer JSON de bloques de código markdown
      cleanOutput = this.extractFromMarkdownBlock(cleanOutput);

      // Si no hay bloques de código, buscar directamente el JSON
      if (!cleanOutput.includes('```')) {
        cleanOutput = this.extractDirectJSON(cleanOutput);
      }

      // Intentar parsear el JSON
      const parsedData = JSON.parse(cleanOutput) as AIGameResponse;

      return this.validateParsedData(parsedData, output);

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido al parsear JSON',
        rawOutput: output
      };
    }
  }

  /**
   * Versión simplificada que solo retorna los datos o null
   */
  public extractAIGameData(output: string): AIGameResponse | null {
    const result = this.parseAIGameResponse(output);
    return result.success ? result.data! : null;
  }

  // ========== PROCESAMIENTO INTELIGENTE ==========

  /**
   * Función inteligente que maneja tanto respuestas estructuradas como texto normal
   */
  public processAIResponse(output: string): ProcessedAIResponse {
    if (this.isStructuredResponse(output)) {
      const structured = this.extractAIGameData(output);

      if (structured) {
        console.log(`ℹ️ [${this.serviceName}] Respuesta estructurada procesada:`, {
          respuesta: structured.respuesta.substring(0, 50) + '...',
          pista: structured.pista.substring(0, 30) + '...',
          acertado: structured.acertado,
        });

        return {
          type: 'structured',
          structured: structured,
          originalOutput: output
        };
      } else {
        // Si falla el parsing, usar el texto como fallback
        console.warn(`⚠️ [${this.serviceName}] Fallo parsing estructurado, usando como texto`);
        return {
          type: 'text',
          text: output,
          originalOutput: output
        };
      }
    } else {
      console.log(`ℹ️ [${this.serviceName}] Respuesta de texto normal procesada`);
      return {
        type: 'text',
        text: output,
        originalOutput: output
      };
    }
  }

  /**
   * Función con logging detallado para debugging
   */
  public parseAndLog(output: string, serviceName: string = 'aiService'): AIGameResponse | null {
    // Verificar si es una respuesta estructurada
    if (!this.isStructuredResponse(output)) {
      console.log(`ℹ️ [${serviceName}] Respuesta de texto normal (no JSON):`,
        output.substring(0, 100) + '...');
      return null;
    }

    const result = this.parseAIGameResponse(output);

    if (result.success) {
      console.log(`✅ [${serviceName}] JSON parseado correctamente:`, result.data);
      return result.data!;
    } else {
      console.error(`❌ [${serviceName}] Error parseando respuesta estructurada:`, {
        error: result.error,
        rawOutput: result.rawOutput?.substring(0, 200) + '...'
      });
      return null;
    }
  }

  // ========== MÉTODOS PRIVADOS ==========

  private extractFromMarkdownBlock(output: string): string {
    const jsonBlockRegex = /```(?:json)?\s*\n?([\s\S]*?)\n?```/i;
    const jsonBlockMatch = output.match(jsonBlockRegex);

    if (jsonBlockMatch) {
      return jsonBlockMatch[1].trim();
    }

    return output;
  }

  private extractDirectJSON(output: string): string {
    const directJsonRegex = /\{[\s\S]*\}/;
    const directJsonMatch = output.match(directJsonRegex);

    if (directJsonMatch) {
      return directJsonMatch[0];
    }

    return output;
  }

  private validateParsedData(parsedData: AIGameResponse, originalOutput: string): ParseResult {
    // Validar que contiene los campos esperados
    if (!parsedData || typeof parsedData !== 'object') {
      return {
        success: false,
        error: 'El JSON parseado no es un objeto válido',
        rawOutput: originalOutput
      };
    }

    // Validar campos requeridos
    const requiredFields = ['respuesta', 'pista', 'acertado'];
    const missingFields = requiredFields.filter(field => !(field in parsedData));

    if (missingFields.length > 0) {
      return {
        success: false,
        error: `Campos faltantes: ${missingFields.join(', ')}`,
        rawOutput: originalOutput
      };
    }

    // Normalizar y validar tipos
    const normalizedData: AIGameResponse = {
      respuesta: String(parsedData.respuesta || '').trim(),
      pista: String(parsedData.pista || '').trim(),
      acertado: Boolean(parsedData.acertado),
    };

    // Validar que respuesta no esté vacía
    if (!normalizedData.respuesta) {
      return {
        success: false,
        error: 'La respuesta no puede estar vacía',
        rawOutput: originalOutput
      };
    }

    return {
      success: true,
      data: normalizedData
    };
  }
}

// ========== SINGLETON EXPORT ==========
export const responseParser = new ResponseParser();
