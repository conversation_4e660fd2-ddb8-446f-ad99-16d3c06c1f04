/**
 * ========================================================================
 * AI MODELS & INTERFACES
 * ========================================================================
 *
 * Definiciones de tipos e interfaces para el servicio de IA
 * Centraliza todas las interfaces relacionadas con AI
 * ========================================================================
 */

import type { GameMode } from "../../../contexts/EnygmaGameContext";

// ========== GAME INTERFACES ==========
export interface GameClue {
  id: string;
  text: string;
  sessionId?: string;
  timestamp?: Date;
}

export interface CluesStorage {
  clues: GameClue[];
  lastUpdated: Date;
  sessionId?: string;
}

export interface AIGameResponse {
  respuesta: string;
  pista: string;
  acertado: boolean;
}

// ========== PARSING INTERFACES ==========
export interface ParseResult {
  success: boolean;
  data?: AIGameResponse;
  error?: string;
  rawOutput?: string;
}

export interface ProcessedAIResponse {
  type: 'structured' | 'text';
  structured?: AIGameResponse;
  text?: string;
  originalOutput: string;
}

// ========== SESSION MANAGEMENT ==========
export interface AISessionConfig {
  sessionId: string | null;
  mode: GameMode | null;
  character: string | null;
  startTime: Date | null;
  lastActivity: Date | null;
  messageCount: number;
  cluesGenerated: GameClue[];
}

export interface SessionStats {
  requestsCount: number;
  responsesParsed: number;
  cluesGenerated: number;
  errorsCount: number;
  averageResponseTime: number;
}

// ========== API PAYLOAD INTERFACES ==========
export interface GenerateOptions {
  query: string;
  mode: GameMode;
  character?: string;
  sessionId?: string;
  customTemplate?: string;
  maxTokens?: number;
}

export interface PayloadTemplate {
  template?: string;
  preamble?: string;
  examples?: string[];
}

// ========== SERVICE CONFIGURATION ==========
export interface AIServiceConfig {
  serviceName: string;
  autoSpeechEnabled: boolean;
  debugMode: boolean;
  retryAttempts: number;
  requestTimeout: number;
}

export interface AIServiceStats {
  sessionsCreated: number;
  requestsCount: number;
  responsesParsed: number;
  cluesGenerated: number;
  charactersGenerated: number;
  errorsCount: number;
  lastActivity: Date;
}

// ========== RESPONSE HANDLING ==========
export interface ResponseHandlerResult {
  type: 'structured' | 'text';
  content: string;
  clue?: string;
  isCorrect?: boolean;
  confidence?: number;
}

// ========== ERROR HANDLING ==========
export interface AIServiceError {
  code: string;
  message: string;
  context?: any;
  timestamp: Date;
  operation: string;
}

// ========== VALIDATION ==========
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
